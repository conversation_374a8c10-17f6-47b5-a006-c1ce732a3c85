# Portfolio Correctie Systeem

Een complete oplossing voor het verwerken van portfolio documenten naar gestructureerde JSON, bestaande uit een FastAPI backend en een moderne web frontend.

## 🎯 Overzicht

Dit systeem converteert ongestructureerde portfolio teksten naar geanalyseerde JSON data, geschikt voor competentie-beoordeling en verdere analyse.

### ✨ Hoofdfuncties
- **Automatische sectie detectie** (3.1, 4.2, etc.)
- **Paragraaf splitsing** voor gedetailleerde analyse
- **Content filtering** (verwijdering samenvattende teksten)
- **Dubbele sectie handling** 
- **Numerieke sortering** van secties
- **Web interface** voor eenvoudig gebruik

## 🏗️ Architectuur

```
portfolio_correctie/
├── backend/                    # FastAPI Backend
│   ├── api/
│   │   ├── main.py            # FastAPI applicatie
│   │   └── portfolio_processor.py # Core processing logic
│   ├── requirements.txt       # Python dependencies
│   └── README.md             # Backend documentatie
├── frontend/                  # Web Frontend
│   ├── index.html            # Hoofdpagina
│   ├── styles.css            # Styling
│   ├── script.js             # JavaScript functionaliteit
│   └── README.md             # Frontend documentatie
├── input/                    # Originele TXT bestanden (legacy)
├── output_json_enhanced/     # JSON output (legacy)
├── output_text/              # Processed TXT (legacy)
├── main.py                   # Legacy batch processor
└── README.md                 # Deze documentatie
```

## 🚀 Quick Start

### 1. Backend starten
```bash
# Navigeer naar backend
cd backend

# Installeer dependencies
pip install -r requirements.txt

# Start API server
cd api
python main.py
```
Backend draait nu op: `http://localhost:8000`

### 2. Frontend openen
```bash
# Navigeer naar frontend
cd frontend

# Optie 1: Direct openen
open index.html

# Optie 2: Lokale server
python -m http.server 3000
```
Frontend beschikbaar op: `http://localhost:3000`

### 3. Portfolio verwerken
1. Open de web interface
2. Plak portfolio tekst in het linker vak
3. Klik "Verwerken"
4. Bekijk gestructureerde output rechts

## 📊 Voorbeeld

### Input
```
3.1 Je maakt veiligheid bespreekbaar

Mevrouw Ferrier toont in de beschreven casus aan dat zij veiligheid bespreekbaar maakt...

3.2 Je stemt continu samen de verwachtingen af

Mevrouw Ferrier toont in de beschreven casus met moeder A...
```

### Output (JSON)
```json
{
  "total_sections": 2,
  "sections": {
    "3.1": {
      "title": "3.1",
      "total_paragraphs": 2,
      "paragraphs": {
        "paragraph_1": {"content": "3.1"},
        "paragraph_2": {"content": "Je maakt veiligheid bespreekbaar"}
      }
    }
  }
}
```

## 🔧 API Endpoints

### `POST /process`
Hoofdendpoint voor portfolio verwerking
- **Input**: `{"tekst": "portfolio content..."}`
- **Output**: Gestructureerde JSON met success/error wrapper

### `GET /health`
Health check voor monitoring

### `GET /docs`
Automatische API documentatie (Swagger UI)

## 💡 Use Cases

### Voor Beoordelaars
- Snelle analyse van portfolio structuur
- Overzichtelijke sectie-indeling
- Geautomatiseerde competentie mapping

### Voor Studenten
- Validatie van portfolio structuur
- Controle op completheid
- Feedback op organisatie

### Voor Systemen
- Gestructureerde data voor databases
- API integratie met andere tools
- Batch verwerking mogelijk

## 🛠️ Technische Details

### Backend
- **Framework**: FastAPI
- **Taal**: Python 3.8+
- **Dependencies**: Pydantic, Uvicorn
- **Features**: CORS, Auto-docs, Type hints

### Frontend
- **Technologie**: Vanilla JavaScript, CSS Grid/Flexbox
- **Features**: Responsive design, Real-time feedback
- **Browser Support**: Modern browsers (Chrome 60+, Firefox 55+)

### Processing Logic
- **Regex patterns** voor sectie detectie
- **Text splitting** algoritmes
- **Deduplication** logic
- **Numerical sorting** voor correcte volgorde

## 📈 Monitoring

### API Status
- Health check endpoint: `/health`
- Real-time status in frontend
- Error logging en handling

### Performance
- Lightweight processing
- Minimal dependencies
- Fast response times

## 🔄 Migration van Legacy

Het systeem bevat nog de originele `main.py` voor batch verwerking:

```bash
# Legacy batch processing (nog steeds functioneel)
python main.py
```

Dit verwerkt alle bestanden in `input/` naar `output_json_enhanced/` en `output_text/`.

## 🚀 Deployment

### Development
```bash
# Backend
cd backend/api && python main.py

# Frontend
cd frontend && python -m http.server 3000
```

### Production
```bash
# Backend met Gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker api.main:app

# Frontend met Nginx of Apache
# Serve static files from frontend/
```

## 📝 Licentie

Dit project is ontwikkeld voor EVC Academie portfolio correctie.

## 🤝 Bijdragen

Voor vragen of verbeteringen, neem contact op met het development team.
