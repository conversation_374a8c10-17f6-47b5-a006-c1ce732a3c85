<!DOCTYPE html>
<html lang="nl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Portfolio Correctie Tool</title>
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <div class="container">
      <header>
        <h1>Portfolio Correctie Tool</h1>
        <p class="subtitle">
          Converteer portfolio tekst naar gestructureerde JSON
        </p>
      </header>

      <main>
        <div class="input-section">
          <h2>📝 Invoer</h2>
          <div class="textarea-container">
            <label for="input-text">Plak hier je portfolio tekst:</label>
            <textarea
              id="input-text"
              placeholder="Plak hier je portfolio tekst met sectienummers (bijv. 3.1, 4.2)...

Voorbeeld:
3.1 Je maakt veiligheid bespreekbaar

Dit is een voorbeeld van portfolio tekst die geanalyseerd wordt op competenties...

3.2 Je stemt continu samen de verwachtingen af

Hier komt de tekst voor de volgende competentie..."
              rows="15"
            ></textarea>
            <div class="char-count">
              <span id="char-count">0 karakters</span>
            </div>
          </div>

          <div class="controls">
            <button id="process-btn" class="btn-primary">
              <span class="btn-text">🔄 Verwerken</span>
              <span class="loading-spinner" style="display: none"
                >⏳ Verwerken...</span
              >
            </button>
            <button id="clear-btn" class="btn-secondary">🗑️ Wissen</button>
            <button id="example-btn" class="btn-secondary">
              📋 Voorbeeld laden
            </button>
          </div>
        </div>

        <div class="output-section">
          <h2>📊 Resultaat</h2>
          <div class="output-controls">
            <button id="copy-btn" class="btn-secondary" disabled>
              📋 Kopiëren
            </button>
            <button id="download-btn" class="btn-secondary" disabled>
              💾 Downloaden
            </button>
            <div class="format-toggle">
              <label>
                <input type="radio" name="format" value="json" /> JSON
              </label>
              <label>
                <input type="radio" name="format" value="formatted" checked />
                Geformatteerd
              </label>
            </div>
          </div>

          <div class="textarea-container">
            <textarea
              id="output-text"
              placeholder="Hier verschijnt het verwerkte resultaat..."
              rows="15"
              readonly
            ></textarea>
          </div>

          <div class="stats" id="stats" style="display: none">
            <div class="stat-item">
              <span class="stat-label">Secties:</span>
              <span class="stat-value" id="section-count">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Paragrafen:</span>
              <span class="stat-value" id="paragraph-count">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Status:</span>
              <span class="stat-value" id="process-status"
                >Klaar voor verwerking</span
              >
            </div>
          </div>
        </div>

        <div class="changes-section" id="changes-section" style="display: none">
          <h2>🔍 Wijzigingen Overzicht</h2>

          <div class="changes-tabs">
            <button class="tab-btn active" data-tab="summary">
              📈 Samenvatting
            </button>
            <button class="tab-btn" data-tab="removed">🗑️ Verwijderd</button>
            <button class="tab-btn" data-tab="structure">🏗️ Structuur</button>
          </div>

          <div class="tab-content">
            <div class="tab-panel active" id="summary-panel">
              <div class="summary-stats">
                <div class="summary-item">
                  <span class="summary-icon">📝</span>
                  <div class="summary-details">
                    <span class="summary-label">Gedetecteerde Secties</span>
                    <span class="summary-value" id="detected-sections">0</span>
                  </div>
                </div>
                <div class="summary-item">
                  <span class="summary-icon">🗑️</span>
                  <div class="summary-details">
                    <span class="summary-label"
                      >Verwijderde Samenvattingen</span
                    >
                    <span class="summary-value" id="removed-summaries">0</span>
                  </div>
                </div>
                <div class="summary-item">
                  <span class="summary-icon">🔄</span>
                  <div class="summary-details">
                    <span class="summary-label">Verwijderde Duplicaten</span>
                    <span class="summary-value" id="removed-duplicates">0</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="tab-panel" id="removed-panel">
              <div class="removed-content">
                <div class="removed-section">
                  <h4>🗑️ Verwijderde Samenvattende Paragrafen</h4>
                  <div class="removed-list" id="removed-summaries-list">
                    <p class="no-items">
                      Geen samenvattende paragrafen verwijderd
                    </p>
                  </div>
                </div>
                <div class="removed-section">
                  <h4>🔄 Verwijderde Dubbele Paragrafen</h4>
                  <div class="removed-list" id="removed-duplicates-list">
                    <p class="no-items">Geen dubbele paragrafen verwijderd</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="tab-panel" id="structure-panel">
              <div class="structure-content">
                <h4>🏗️ Gedetecteerde Sectie Structuur</h4>
                <div class="structure-list" id="structure-list">
                  <p class="no-items">Geen secties gedetecteerd</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <div class="error-message" id="error-message" style="display: none">
        <div class="error-content">
          <span class="error-icon">⚠️</span>
          <span class="error-text"></span>
          <button class="error-close" onclick="hideError()">✕</button>
        </div>
      </div>

      <footer>
        <div class="info-section">
          <h3>ℹ️ Instructies</h3>
          <ul>
            <li>
              <strong>Sectienummers:</strong> Gebruik formaat zoals 3.1, 4.2,
              etc.
            </li>
            <li>
              <strong>Structuur:</strong> Elke sectie wordt automatisch
              gedetecteerd
            </li>
            <li>
              <strong>Filtering:</strong> "Samenvattend" paragrafen worden
              automatisch verwijderd
            </li>
            <li>
              <strong>Output:</strong> JSON formaat geschikt voor verdere
              analyse
            </li>
          </ul>
        </div>

        <div class="api-info">
          <p>
            API Status:
            <span id="api-status" class="status-unknown">Controleren...</span>
          </p>
          <p>Versie: 1.0.0</p>
        </div>
      </footer>
    </div>

    <script src="script.js"></script>
  </body>
</html>
