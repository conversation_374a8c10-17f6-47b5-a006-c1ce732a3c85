/* Portfolio Correctie Tool - Styling */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #e0e0e0;
}

header h1 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 700;
}

.subtitle {
    color: #7f8c8d;
    font-size: 1.2em;
    font-weight: 300;
}

/* Main Layout */
main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.input-section, .output-section {
    background: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8e8e8;
}

.input-section h2, .output-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5em;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Textarea Styling */
.textarea-container {
    position: relative;
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.3s ease;
    background: #fafafa;
}

textarea:focus {
    outline: none;
    border-color: #667eea;
    background: #fff;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

textarea::placeholder {
    color: #999;
    font-style: italic;
}

.char-count {
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 12px;
    color: #888;
    background: rgba(255, 255, 255, 0.9);
    padding: 2px 6px;
    border-radius: 4px;
}

/* Buttons */
.controls, .output-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.btn-primary, .btn-secondary {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 2px solid #e9ecef;
}

.btn-secondary:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #dee2e6;
}

.btn-secondary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Format Toggle */
.format-toggle {
    display: flex;
    gap: 15px;
    margin-left: auto;
}

.format-toggle label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    cursor: pointer;
}

.format-toggle input[type="radio"] {
    margin: 0;
}

/* Statistics */
.stats {
    display: flex;
    gap: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 15px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    font-weight: 600;
    text-transform: uppercase;
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
}

/* Error Message */
.error-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #dc3545;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
    z-index: 1000;
    max-width: 400px;
    animation: slideIn 0.3s ease;
}

.error-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.error-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    margin-left: auto;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Footer */
footer {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    padding: 20px 0;
    border-top: 2px solid #e0e0e0;
    margin-top: 20px;
}

.info-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.info-section ul {
    list-style: none;
    padding-left: 0;
}

.info-section li {
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.info-section li::before {
    content: "•";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.api-info {
    text-align: right;
    color: #666;
}

.status-unknown { color: #ffc107; }
.status-online { color: #28a745; }
.status-offline { color: #dc3545; }

/* Responsive Design */
@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    footer {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .api-info {
        text-align: center;
    }
    
    .controls, .output-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .format-toggle {
        margin-left: 0;
        justify-content: center;
    }
}
