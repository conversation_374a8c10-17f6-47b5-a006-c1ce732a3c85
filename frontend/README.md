# Portfolio Correctie Tool - Frontend

Gebruiksvriendelijke web interface voor het verwerken van portfolio documenten.

## Functionaliteit

### 🔧 Core Features
- **Tekst Input**: Groot tekstvak voor portfolio tekst
- **Real-time Processing**: Directe verwerking via backend API
- **Dual Output**: JSON en geformatteerde weergave
- **Statistics**: Overzicht van secties en paragrafen
- **Error Handling**: Duidelijke foutmeldingen

### 📱 User Experience
- **Responsive Design**: Werkt op desktop en mobiel
- **Character Counter**: Real-time karakter telling
- **Copy/Download**: Resultaten kopiëren of downloaden
- **Example Data**: Voorbeeldtekst voor testing
- **Keyboard Shortcuts**: Sneltoetsen voor efficiency

### 🎨 Interface
- **Modern Design**: Clean en professioneel uiterlijk
- **Visual Feedback**: Loading states en success indicators
- **API Status**: Real-time backend verbinding status
- **Format Toggle**: Schak<PERSON> tussen JSON en leesbare output

## Gebruik

### 1. **Start de Backend**
Zorg dat de backend API draait op `http://localhost:8000`

### 2. **Open de Frontend**
Open `index.html` in je browser of gebruik een lokale server:

```bash
# Optie 1: Direct openen
open index.html

# Optie 2: Python server
python -m http.server 3000

# Optie 3: Node.js server
npx serve .
```

### 3. **Portfolio Verwerken**
1. Plak portfolio tekst in het linker tekstvak
2. Klik op "🔄 Verwerken" of gebruik `Ctrl+Enter`
3. Bekijk resultaat in rechter tekstvak
4. Kopieer of download het resultaat

## Keyboard Shortcuts

- `Ctrl + Enter`: Verwerk tekst
- `Ctrl + L`: Laad voorbeeld
- `Ctrl + K`: Wis alles

## Ondersteunde Formaten

### Input
- Portfolio tekst met sectienummers (3.1, 4.2, etc.)
- Automatische detectie van competentie secties
- Filtering van samenvattende paragrafen

### Output
- **JSON**: Gestructureerde data voor verdere verwerking
- **Geformatteerd**: Leesbare tekst voor review

## Technische Details

### Dependencies
- Vanilla JavaScript (geen frameworks)
- Modern CSS (Grid, Flexbox)
- Fetch API voor backend communicatie

### Browser Support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### API Integration
- RESTful communicatie met backend
- Automatic error handling
- Real-time status monitoring

## Bestandsstructuur

```
frontend/
├── index.html          # Hoofdpagina
├── styles.css          # Styling
├── script.js           # JavaScript functionaliteit
└── README.md          # Deze documentatie
```

## Configuratie

### API URL aanpassen
In `script.js`, regel 4:
```javascript
const API_BASE_URL = 'http://localhost:8000';
```

### Styling aanpassen
Pas `styles.css` aan voor custom branding of layout wijzigingen.

## Features in Detail

### Error Handling
- Network errors
- API response errors
- Input validation
- User-friendly messages

### Statistics
- Aantal gevonden secties
- Totaal aantal paragrafen
- Processing status
- API verbinding status

### Responsive Design
- Mobile-first approach
- Flexible grid layout
- Touch-friendly buttons
- Optimized for all screen sizes

## Development

### Local Testing
```bash
# Start backend
cd ../backend/api
python main.py

# Start frontend (in nieuwe terminal)
cd frontend
python -m http.server 3000
```

### Debugging
- Open browser Developer Tools
- Check Console voor JavaScript errors
- Network tab voor API calls
- Responsive mode voor mobile testing
