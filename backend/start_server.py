#!/usr/bin/env python3
"""
Server starter script
"""

import uvicorn
import sys
import os

# Voeg de huidige directory toe aan het Python path
sys.path.insert(0, os.path.dirname(__file__))

if __name__ == "__main__":
    print("🚀 Starting Portfolio Correctie API server...")
    print("📍 Server will be available at: http://localhost:8800")
    print("📖 API docs will be available at: http://localhost:8800/docs")
    print("=" * 50)
    
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8800,
        reload=True,
        log_level="info"
    )
