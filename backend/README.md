# Portfolio Correctie API

Backend API voor het verwerken van portfolio documenten naar gestructureerde JSON.

## Functionaliteit

- **Sectie detectie**: Automatische herkenning van genummerde secties (3.1, 4.2, etc.)
- **Paragraaf splitsing**: Opdeling van tekst in beheerbare blokken
- **Content filtering**: Verwijdering van samenvattende paragrafen
- **Dubbele sectie handling**: Automatische deduplicatie
- **Numerieke sortering**: Logische volgorde van secties

## Installatie

1. **Navigeer naar backend directory:**
```bash
cd backend
```

2. **Installeer dependencies:**
```bash
pip install -r requirements.txt
```

3. **Start de API server:**
```bash
cd api
python main.py
```

De API draait nu op: `http://localhost:8000`

## API Endpoints

### `GET /`
Health check endpoint

### `POST /process`
Hoofdendpoint voor portfolio verwerking

**Request:**
```json
{
  "tekst": "3.1 Je maakt veiligheid bespreekbaar\n\nDit is een voorbeeld..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_sections": 1,
    "sections": {
      "3.1": {
        "title": "3.1",
        "total_paragraphs": 2,
        "paragraphs": {
          "paragraph_1": {"content": "3.1"},
          "paragraph_2": {"content": "Je maakt veiligheid bespreekbaar"}
        }
      }
    }
  },
  "error": null
}
```

### `POST /process-json`
Alternatief endpoint dat directe JSON retourneert (zonder wrapper)

### `GET /health`
Gedetailleerde health check

### `GET /stats`
API statistieken en ondersteunde features

## API Documentatie

Automatische API documentatie beschikbaar op:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Development

### Testing
```bash
pytest
```

### Productie deployment
```bash
gunicorn -w 4 -k uvicorn.workers.UvicornWorker api.main:app
```

## Architectuur

```
backend/
├── api/
│   ├── main.py                 # FastAPI applicatie
│   └── portfolio_processor.py  # Core processing logic
├── requirements.txt            # Python dependencies
└── README.md                  # Deze documentatie
```
