#!/usr/bin/env python3
"""
Test script voor de backend functionaliteit
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from portfolio_processor import verwerk_portfolio_tekst

def test_wijzigingen_functionaliteit():
    """Test de nieuwe wijzigingen functionaliteit"""
    
    test_tekst = """
3.1 Je maakt veiligheid bespreekbaar

Dit is een voorbeeld van portfolio tekst die geanalyseerd wordt op competenties.

Samenvattend kan gesteld worden dat dit een test is.

3.2 Je stemt continu samen de verwachtingen af

Hier komt de tekst voor de volgende competentie.

Dit is een voorbeeld van portfolio tekst die geanalyseerd wordt op competenties.

Samenvattend is dit een duplicaat test.
"""
    
    print("🧪 Test: Verwerking van portfolio tekst met wijzigingen tracking")
    print("=" * 60)
    
    try:
        resultaat = verwerk_portfolio_tekst(test_tekst)
        
        print("✅ Verwerking succesvol!")
        print(f"📊 Aantal secties: {resultaat['total_sections']}")
        
        if 'wijzigingen' in resultaat:
            wijzigingen = resultaat['wijzigingen']
            statistieken = wijzigingen['statistieken']
            
            print("\n🔍 Wijzigingen overzicht:")
            print(f"   📝 Gedetecteerde secties: {statistieken['totaal_gedetecteerde_secties']}")
            print(f"   🗑️ Verwijderde samenvattingen: {statistieken['aantal_verwijderde_samenvattend']}")
            print(f"   🔄 Verwijderde duplicaten: {statistieken['aantal_verwijderde_duplicaten']}")
            
            print("\n📋 Verwijderde samenvattende paragrafen:")
            for i, paragraaf in enumerate(wijzigingen['verwijderde_samenvattend_paragrafen'], 1):
                print(f"   {i}. {paragraaf}")
            
            print("\n📋 Verwijderde dubbele paragrafen:")
            for i, paragraaf in enumerate(wijzigingen['verwijderde_dubbele_paragrafen'], 1):
                print(f"   {i}. {paragraaf}")
            
            print("\n🏗️ Sectie structuur:")
            for sectie in wijzigingen['sectie_structuur']:
                print(f"   {sectie['nummer']}: {sectie['paragraaf_count']} paragrafen")
        else:
            print("❌ Geen wijzigingen data gevonden!")
            
    except Exception as e:
        print(f"❌ Fout bij verwerking: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_wijzigingen_functionaliteit()
