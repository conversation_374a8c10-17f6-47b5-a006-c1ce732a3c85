from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any
import json

from portfolio_processor import verwerk_portfolio_tekst

# FastAPI app initialisatie
app = FastAPI(
    title="Portfolio Correctie API",
    description="API voor het verwerken van portfolio documenten naar gestructureerde JSON",
    version="1.0.0"
)

# CORS middleware toevoegen voor frontend communicatie
# USECASE: Staat frontend toe om API aan te roepen vanaf andere poorten/domeinen
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In productie: specificeer exacte frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response modellen
class PortfolioInput(BaseModel):
    """
    Input model voor portfolio tekst

    USECASE: Valideert dat de frontend correcte data stuurt
    """
    tekst: str

    class Config:
        schema_extra = {
            "example": {
                "tekst": "3.1 Je maakt veiligheid bespreekbaar\n\nDit is een voorbeeld van portfolio tekst..."
            }
        }

class PortfolioOutput(BaseModel):
    """
    Output model voor verwerkte portfolio data

    USECASE: Gestructureerde response voor frontend
    """
    success: bool
    data: Dict[str, Any] = None
    error: str = None

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "total_sections": 2,
                    "sections": {
                        "3.1": {
                            "title": "3.1",
                            "total_paragraphs": 3,
                            "paragraphs": {
                                "paragraph_1": {"content": "3.1"},
                                "paragraph_2": {"content": "Je maakt veiligheid bespreekbaar"},
                                "paragraph_3": {"content": "Dit is een voorbeeld..."}
                            }
                        }
                    }
                },
                "error": None
            }
        }

# API Endpoints
@app.get("/")
async def root():
    """
    Health check endpoint

    USECASE: Controleren of de API draait
    """
    return {
        "message": "Portfolio Correctie API is actief",
        "version": "1.0.0",
        "status": "healthy"
    }

@app.get("/health")
async def health_check():
    """
    Gedetailleerde health check

    USECASE: Monitoring en debugging
    """
    return {
        "status": "healthy",
        "service": "portfolio-correctie-api",
        "version": "1.0.0"
    }

@app.post("/process", response_model=PortfolioOutput)
async def process_portfolio(input_data: PortfolioInput):
    """
    Hoofdendpoint voor portfolio verwerking

    USECASE: Ontvangt portfolio tekst van frontend en retourneert gestructureerde JSON

    Args:
        input_data: PortfolioInput object met de te verwerken tekst

    Returns:
        PortfolioOutput: Gestructureerde JSON met secties en paragrafen

    Raises:
        HTTPException: Bij verwerkingsfouten
    """
    try:
        # Valideer input
        if not input_data.tekst or not input_data.tekst.strip():
            raise HTTPException(
                status_code=400,
                detail="Tekst mag niet leeg zijn"
            )

        # Verwerk de portfolio tekst
        verwerkte_data = verwerk_portfolio_tekst(input_data.tekst)

        # Controleer of er secties gevonden zijn
        if not verwerkte_data.get("sections"):
            return PortfolioOutput(
                success=False,
                error="Geen geldige secties gevonden in de tekst. Zorg ervoor dat de tekst sectienummers bevat (bijv. 3.1, 4.2)."
            )

        return PortfolioOutput(
            success=True,
            data=verwerkte_data
        )

    except Exception as e:
        # Log de fout (in productie: gebruik proper logging)
        print(f"Fout bij verwerken portfolio: {str(e)}")

        return PortfolioOutput(
            success=False,
            error=f"Er is een fout opgetreden bij het verwerken: {str(e)}"
        )

@app.post("/process-json")
async def process_portfolio_raw_json(input_data: PortfolioInput):
    """
    Alternatief endpoint dat directe JSON retourneert

    USECASE: Voor directe integratie zonder wrapper object
    """
    try:
        if not input_data.tekst or not input_data.tekst.strip():
            raise HTTPException(
                status_code=400,
                detail="Tekst mag niet leeg zijn"
            )

        verwerkte_data = verwerk_portfolio_tekst(input_data.tekst)

        if not verwerkte_data.get("sections"):
            raise HTTPException(
                status_code=422,
                detail="Geen geldige secties gevonden in de tekst"
            )

        return verwerkte_data

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Verwerkingsfout: {str(e)}"
        )

# Optioneel: Endpoint voor het ophalen van statistieken
@app.get("/stats")
async def get_processing_stats():
    """
    Endpoint voor basis statistieken

    USECASE: Monitoring en analytics
    """
    return {
        "supported_formats": ["txt"],
        "supported_section_patterns": ["X.Y (bijv. 3.1, 4.2)"],
        "features": [
            "Automatische sectie detectie",
            "Paragraaf splitsing",
            "Samenvattend paragrafen filtering",
            "Dubbele sectie verwijdering",
            "Numerieke sortering"
        ]
    }

if __name__ == "__main__":
    import uvicorn

    # USECASE: Lokale development server
    # In productie: gebruik gunicorn of vergelijkbare WSGI server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8800,
        reload=True,  # Auto-reload bij code wijzigingen
        log_level="info"
    )
