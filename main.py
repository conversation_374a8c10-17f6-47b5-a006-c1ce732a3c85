import re
import json
from pathlib import Path

# Termen die aangeven dat paragrafen overgeslagen moeten worden
# USECASE: Portfolio documenten bevatten vaak automatisch gegenereerde teksten
# die niet relevant zijn voor competentie-beoordeling
OVERSLAAN_TERMEN = ['samenvattend', 'report a bug']

def verwijder_dubbele_secties(secties):
    """
    Verwijder dubbele sectienummers, behoud degene met de meeste inhoud

    USECASE: Soms bevatten documenten hetzelfde sectienummer meerdere keren
    (bijv. 3.1 komt twee keer voor). Dit kan gebeuren door:
    - Kopieer/plak fouten in het originele document
    - Herstructurering waarbij secties verplaatst maar niet verwijderd werden
    - Samenvoegen van meerdere documenten

    Deze functie zorgt ervoor dat we alleen de meest complete versie behouden,
    zodat de JSON output geen dubbele secties bevat.
    """
    sectie_dict = {}

    for sectie in secties:
        sectie_nummer = sectie['nummer']

        # Als we dit sectienummer nog niet hebben gezien, of als deze sectie meer inhoud heeft
        if (sectie_nummer not in sectie_dict or
            len(sectie['inhoud']) > len(sectie_dict[sectie_nummer]['inhoud'])):
            sectie_dict[sectie_nummer] = sectie

    # Geef secties terug in oorspronkelijke volgorde
    unieke_secties = []
    gezien_nummers = set()

    for sectie in secties:
        if sectie['nummer'] not in gezien_nummers:
            gezien_nummers.add(sectie['nummer'])
            unieke_secties.append(sectie_dict[sectie['nummer']])

    return unieke_secties

def verwijder_samenvattend_paragrafen(inhoud):
    """
    Verwijder alleen individuele paragrafen die beginnen met 'Samenvattend'

    USECASE: Portfolio documenten bevatten vaak samenvattende paragrafen die:
    - Automatisch gegenereerd zijn door systemen
    - Redundante informatie bevatten die al in de hoofdtekst staat
    - De leesbaarheid van de uiteindelijke output verstoren
    - Niet relevant zijn voor de analyse van competenties

    Deze functie filtert deze paragrafen eruit terwijl belangrijke inhoud behouden blijft.
    Bijvoorbeeld: "Samenvattend kan gesteld worden..." wordt verwijderd.
    """
    # Splits inhoud in paragrafen (door dubbele nieuwe regels)
    paragrafen = inhoud.split('\n\n')
    gefilterde_paragrafen = []

    for paragraaf in paragrafen:
        # Sla alleen paragrafen over die beginnen met 'Samenvattend' en op zichzelf staan
        # Sla niet over als de paragraaf sectiekoppen of andere belangrijke inhoud bevat
        regels_in_paragraaf = [regel.strip() for regel in paragraaf.split('\n') if regel.strip()]

        # Als de paragraaf slechts één regel heeft en begint met 'Samenvattend', sla over
        if (len(regels_in_paragraaf) == 1 and
            regels_in_paragraaf[0].lower().startswith(tuple(OVERSLAAN_TERMEN))):
            continue

        # Als de paragraaf begint met 'Samenvattend' maar meerdere regels heeft,
        # verwijder alleen de 'Samenvattend' regel(s) en behoud de rest
        if regels_in_paragraaf and regels_in_paragraaf[0].lower().startswith(tuple(OVERSLAAN_TERMEN)):
            # Vind alle regels die beginnen met 'Samenvattend' of 'Report a Bug'
            gefilterde_regels = []
            for regel in paragraaf.split('\n'):
                if not any(regel.strip().lower().startswith(term) for term in OVERSLAAN_TERMEN):
                    gefilterde_regels.append(regel)

            # Voeg alleen toe als er resterende regels zijn
            if any(regel.strip() for regel in gefilterde_regels):
                gefilterde_paragrafen.append('\n'.join(gefilterde_regels))
        else:
            # Behoud de paragraaf zoals deze is
            gefilterde_paragrafen.append(paragraaf)

    return '\n\n'.join(gefilterde_paragrafen)

def verwijder_dubbele_paragrafen(paragrafen):
    """
    Verwijder dubbele paragrafen, behoud alleen de laatste van identieke paragrafen

    USECASE: Soms bevatten documenten identieke paragrafen door:
    - Kopieer/plak fouten in het originele document
    - Herstructurering waarbij content gedupliceerd werd
    - Automatische generatie die duplicaten creëert

    Deze functie zorgt ervoor dat we alleen de laatste versie behouden,
    zodat de JSON output geen dubbele paragrafen bevat.
    """
    if not paragrafen:
        return paragrafen

    # Houd bij welke content we al hebben gezien en op welke positie
    gezien_content = {}
    te_verwijderen_indices = set()

    for i, paragraaf in enumerate(paragrafen):
        content = paragraaf.strip()

        # Sla lege paragrafen over
        if not content:
            continue

        # Als we deze content al eerder hebben gezien
        if content in gezien_content:
            # Markeer de eerdere versie voor verwijdering
            te_verwijderen_indices.add(gezien_content[content])

        # Update de positie van deze content (laatste versie)
        gezien_content[content] = i

    # Maak nieuwe lijst zonder de te verwijderen indices
    gefilterde_paragrafen = []
    for i, paragraaf in enumerate(paragrafen):
        if i not in te_verwijderen_indices:
            gefilterde_paragrafen.append(paragraaf)

    return gefilterde_paragrafen

def splits_inhoud_in_paragrafen(inhoud):
    """
    Splits inhoud in paragrafen/regels als subnodes

    USECASE: Voor gedetailleerde analyse moeten we tekst opdelen in kleine, beheerbare stukken.
    Dit is nodig omdat:
    - AI-systemen beter werken met kleinere tekstblokken
    - Gebruikers specifieke paragrafen kunnen selecteren voor feedback
    - Elke paragraaf apart geanalyseerd kan worden op competenties
    - JSON-structuur overzichtelijker wordt met genummerde paragrafen

    Speciale behandeling: Sectienummers (3.1) en titels worden gescheiden in aparte paragrafen
    voor betere leesbaarheid en analyse. Herkent ook sectietitels die op een aparte regel staan.
    """
    # Opmerking: "Samenvattend" paragrafen worden verwijderd op documentniveau, niet per sectie

    # Splits op dubbele nieuwe regels (paragrafen)
    paragrafen = inhoud.split('\n\n')

    # Verwerk paragrafen om sectienummers te behandelen
    verwerkte_paragrafen = []
    i = 0

    while i < len(paragrafen):
        paragraaf = paragrafen[i].strip()
        if not paragraaf:
            i += 1
            continue

        # Controleer of paragraaf alleen een sectienummer is (bijv. "3.1")
        alleen_sectie_nummer_match = re.match(r'^(\d+\.\d+)$', paragraaf)

        if alleen_sectie_nummer_match:
            sectie_nummer = alleen_sectie_nummer_match.group(1)
            verwerkte_paragrafen.append(sectie_nummer)

            # Controleer of de volgende paragraaf een titel is
            if i + 1 < len(paragrafen):
                volgende_paragraaf = paragrafen[i + 1].strip()

                # Als de volgende paragraaf begint met een hoofdletter en geen sectienummer is,
                # behandel het als een titel
                if (volgende_paragraaf and
                    not re.match(r'^\d+\.\d+', volgende_paragraaf) and
                    volgende_paragraaf[0].isupper()):

                    verwerkte_paragrafen.append(volgende_paragraaf)
                    i += 2  # Sla beide paragrafen over
                    continue

            i += 1
            continue

        # Controleer of paragraaf begint met sectienummer gevolgd door tekst (bijv. "3.1 Titel tekst")
        sectie_met_titel_match = re.match(r'^(\d+\.\d+)\.?\s+(.+)$', paragraaf)
        if sectie_met_titel_match:
            # Splits in twee aparte paragrafen
            sectie_nummer = sectie_met_titel_match.group(1)
            titel_tekst = sectie_met_titel_match.group(2)
            verwerkte_paragrafen.append(sectie_nummer)
            verwerkte_paragrafen.append(titel_tekst)
        else:
            verwerkte_paragrafen.append(paragraaf)

        i += 1

    # Verwijder dubbele paragrafen (behoud alleen de laatste van identieke paragrafen)
    verwerkte_paragrafen = verwijder_dubbele_paragrafen(verwerkte_paragrafen)

    subnodes = {}
    for i, paragraaf in enumerate(verwerkte_paragrafen):
        subnodes[f"paragraph_{i+1}"] = {
            "content": paragraaf
        }

    return subnodes

def splits_tekst_in_secties(tekst_inhoud):
    """
    Splits tekst in logische secties gebaseerd op genummerde patronen zoals 3.1, 3.2, etc.

    USECASE: Portfolio documenten zijn gestructureerd met genummerde competentie-secties.
    Deze functie is essentieel omdat:
    - Elke sectie een specifieke competentie of gedragsindicator vertegenwoordigt
    - Beoordelaars per sectie feedback moeten kunnen geven
    - JSON-output georganiseerd moet zijn per competentiegebied
    - Automatische analyse per competentie mogelijk wordt

    Speciale functionaliteit: Voegt automatisch lege regels toe na sectienummers
    voor consistente opmaak (bijv. na "3.1" komt altijd een lege regel).
    """
    # Patroon om sectiekoppen te matchen zoals "3.1", "4.2", etc.
    sectie_patroon = r'^(\d+\.\d+)(?:\s|$)'

    secties = []
    huidige_sectie = None
    huidige_inhoud = []
    intro_inhoud = []

    regels = tekst_inhoud.split('\n')

    for i, regel in enumerate(regels):
        # Controleer of regel begint met een sectienummer
        match = re.match(sectie_patroon, regel.strip())

        if match:
            # Bewaar vorige sectie als deze bestaat
            if huidige_sectie is not None:
                secties.append({
                    'nummer': huidige_sectie,
                    'inhoud': '\n'.join(huidige_inhoud)
                })

            # Start nieuwe sectie
            huidige_sectie = match.group(1)
            huidige_inhoud = [regel]

            # Controleer of de volgende regel bestaat en niet leeg is
            # Zo ja, voeg een lege regel toe na het sectienummer
            if (i + 1 < len(regels) and
                regels[i + 1].strip() != '' and
                not re.match(sectie_patroon, regels[i + 1].strip())):
                huidige_inhoud.append('')
        else:
            # Voeg regel toe aan huidige sectie of intro
            if huidige_sectie is not None:
                huidige_inhoud.append(regel)
            else:
                # Regels voor eerste sectie - intro inhoud
                intro_inhoud.append(regel)

    # Voeg intro sectie toe als deze inhoud heeft
    if intro_inhoud:
        secties.insert(0, {
            'nummer': 'intro',
            'inhoud': '\n'.join(intro_inhoud)
        })

    # Vergeet de laatste sectie niet
    if huidige_sectie is not None:
        secties.append({
            'nummer': huidige_sectie,
            'inhoud': '\n'.join(huidige_inhoud)
        })

    # Verwijder dubbele secties
    secties = verwijder_dubbele_secties(secties)

    return secties

def verwerk_invoer_bestanden():
    """
    Verwerk alle txt bestanden in de invoer directory en maak verbeterde JSON uitvoer met subnodes

    USECASE: Hoofdfunctie die portfolio documenten omzet naar gestructureerde JSON.
    Dit is de kern van het systeem omdat:
    - Portfolio documenten vaak ongestructureerde tekst zijn
    - JSON-formaat nodig is voor web-applicaties en databases
    - Hiërarchische structuur (secties → paragrafen) analyse vergemakkelijkt
    - Batch-verwerking van meerdere bestanden tegelijk mogelijk is

    Workflow: TXT → Secties splitsen → Paragrafen splitsen → JSON opslaan
    Output: Enhanced JSON bestanden met genummerde secties en paragrafen
    """
    invoer_dir = Path('input')
    uitvoer_dir = Path('output_json_enhanced')

    # Maak uitvoer directory
    uitvoer_dir.mkdir(exist_ok=True)

    # Verwerk elk txt bestand in invoer directory
    for txt_bestand in invoer_dir.glob('*.txt'):
        print(f"Verwerken {txt_bestand.name}...")

        # Lees bestand inhoud
        with open(txt_bestand, 'r', encoding='utf-8') as f:
            inhoud = f.read()

        # Verwijder "Samenvattend" paragrafen van het hele document eerst
        inhoud = verwijder_samenvattend_paragrafen(inhoud)

        # Splits in secties
        secties = splits_tekst_in_secties(inhoud)

        # Maak JSON structuur
        json_data = {
            "filename": txt_bestand.name,
            "total_sections": len(secties),
            "sections": {}
        }

        # Sorteer secties numeriek voordat ze aan JSON worden toegevoegd
        # USECASE: Zorgt ervoor dat secties in logische volgorde staan (4.1, 4.2, 4.10)
        # in plaats van alfabetische volgorde (4.1, 4.10, 4.2)
        def sorteer_sectie_nummer(sectie):
            try:
                # Splits sectienummer zoals "4.1" in [4, 1] en converteer naar integers
                delen = sectie['nummer'].split('.')
                return [int(deel) for deel in delen]
            except ValueError:
                # Als conversie mislukt, gebruik oorspronkelijke string voor sortering
                return [float('inf'), sectie['nummer']]

        # Sorteer secties en voeg elke sectie toe aan de JSON structuur met subnodes
        for sectie in sorted(secties, key=sorteer_sectie_nummer):
            # Sla de intro sectie over
            if sectie['nummer'] == 'intro':
                continue

            # Splits inhoud in paragrafen als subnodes
            subnodes = splits_inhoud_in_paragrafen(sectie['inhoud'])

            json_data["sections"][sectie['nummer']] = {
                "title": sectie['nummer'],
                "total_paragraphs": len(subnodes),
                "paragraphs": subnodes
            }

        # Update totaal aantal secties na verwijdering intro
        json_data["total_sections"] = len(json_data["sections"])

        # Bewaar als JSON bestand
        basis_naam = txt_bestand.stem  # bestandsnaam zonder extensie
        json_bestandsnaam = f"{basis_naam}_enhanced.json"
        json_pad = uitvoer_dir / json_bestandsnaam

        with open(json_pad, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)

        totaal_paragrafen = sum(len(sectie_data["paragraphs"]) for sectie_data in json_data["sections"].values())
        print(f"  Aangemaakt: {json_bestandsnaam} met {len(json_data['sections'])} secties en {totaal_paragrafen} paragrafen")

    print(f"\nAlle bestanden verwerkt. Verbeterde JSON bestanden opgeslagen in '{uitvoer_dir}' directory.")

def genereer_tekst_van_json():
    """
    Genereer tekstbestanden van de verbeterde JSON bestanden

    USECASE: Converteert gestructureerde JSON terug naar leesbare tekst.
    Dit is nuttig voor:
    - Kwaliteitscontrole: controleren of de verwerking correct is verlopen
    - Backup: hebben van een opgeschoonde tekstversie
    - Distributie: delen van documenten in tekstformaat
    - Vergelijking: zien wat er veranderd is na verwerking

    Speciale functionaliteit: Behoudt numerieke volgorde van secties en
    zorgt voor consistente opmaak met lege regels tussen secties.
    """
    json_dir = Path('output_json_enhanced')
    uitvoer_dir = Path('output_text')

    # Maak uitvoer directory
    uitvoer_dir.mkdir(exist_ok=True)

    # Verwerk elk JSON bestand in de json directory
    for json_bestand in json_dir.glob('*_enhanced.json'):
        print(f"Converteren {json_bestand.name} naar tekst...")

        # Lees JSON inhoud
        with open(json_bestand, 'r', encoding='utf-8') as f:
            json_data = json.load(f)

        # Maak tekst inhoud
        tekst_inhoud = []

        # Verwerk elke sectie in numerieke volgorde
        # USECASE: Behoudt logische volgorde bij conversie van JSON naar tekst
        # Essentieel voor leesbaarheid van het eindresultaat
        def sorteer_sectie_sleutel(item):
            sectie_nummer = item[0]
            try:
                # Splits sectienummer zoals "4.1" in [4, 1] en converteer naar integers
                delen = sectie_nummer.split('.')
                return [int(deel) for deel in delen]
            except ValueError:
                # Als conversie mislukt, gebruik oorspronkelijke string voor sortering
                return [float('inf'), sectie_nummer]

        for sectie_nummer, sectie_data in sorted(json_data["sections"].items(), key=sorteer_sectie_sleutel):
            # Voeg sectie titel toe
           # tekst_inhoud.append(sectie_data["title"])

            # Voeg elke paragraaf toe
            for paragraaf_sleutel, paragraaf_data in sorted(sectie_data["paragraphs"].items()):
                tekst_inhoud.append(paragraaf_data["content"])

            # Voeg een lege regel toe tussen secties
            tekst_inhoud.append("")

        # Voeg alle tekst inhoud samen met dubbele nieuwe regels
        finale_tekst = "\n\n".join(tekst_inhoud)

        # Bewaar als tekstbestand
        basis_naam = json_bestand.stem.replace('_enhanced', '')
        tekst_bestandsnaam = f"{basis_naam}_processed.txt"
        tekst_pad = uitvoer_dir / tekst_bestandsnaam

        with open(tekst_pad, 'w', encoding='utf-8') as f:
            f.write(finale_tekst)

        print(f"  Aangemaakt: {tekst_bestandsnaam}")

    print(f"\nAlle JSON bestanden geconverteerd naar tekst. Tekstbestanden opgeslagen in '{uitvoer_dir}' directory.")

if __name__ == "__main__":
    # USECASE: Volledige workflow voor portfolio verwerking
    # Stap 1: TXT bestanden → Gestructureerde JSON (voor analyse/database)
    verwerk_invoer_bestanden()

    # Stap 2: JSON → Opgeschoonde TXT bestanden (voor kwaliteitscontrole)
    genereer_tekst_van_json()
